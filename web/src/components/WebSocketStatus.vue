<template>
  <div class="websocket-status">
    <el-badge 
      :value="unreadCount" 
      :hidden="unreadCount === 0"
      class="status-badge"
    >
      <el-tooltip 
        :content="statusText" 
        placement="bottom"
      >
        <div 
          :class="['status-indicator', statusClass]"
          @click="handleStatusClick"
        >
          <el-icon>
            <component :is="statusIcon" />
          </el-icon>
        </div>
      </el-tooltip>
    </el-badge>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'
import { useUserStore } from '@/pinia/modules/user'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  Loading, 
  Close 
} from '@element-plus/icons-vue'

const webSocketStore = useWebSocketStore()
const userStore = useUserStore()

// 计算属性
const connectionStatus = computed(() => webSocketStore.connectionStatus)
const unreadCount = computed(() => webSocketStore.unreadCount)

const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'connected'
    case 'connecting':
      return 'connecting'
    case 'disconnected':
    default:
      return 'disconnected'
  }
})

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'WebSocket已连接'
    case 'connecting':
      return 'WebSocket连接中...'
    case 'disconnected':
    default:
      return 'WebSocket未连接'
  }
})

const statusIcon = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return Connection
    case 'connecting':
      return Loading
    case 'disconnected':
    default:
      return Close
  }
})

// 点击状态指示器
const handleStatusClick = async () => {
  if (connectionStatus.value === 'disconnected' || connectionStatus.value === 'error') {
    try {
      ElMessage.info('正在尝试重新连接...')
      await webSocketStore.reconnect(userStore.token)
      ElMessage.success('WebSocket重连成功')
    } catch (error) {
      ElMessage.error('WebSocket重连失败: ' + (error.message || '未知错误'))
    }
  } else if (connectionStatus.value === 'connected') {
    // 清除未读消息计数
    webSocketStore.clearUnreadCount()
  }
}

// 自动重连机制
let reconnectInterval = null

const startAutoReconnect = () => {
  // 如果已有重连定时器，先清除
  if (reconnectInterval) {
    clearInterval(reconnectInterval)
  }
  
  // 每30秒检查一次连接状态，如果断开则尝试重连
  reconnectInterval = setInterval(async () => {
    // 只有在用户已登录且WebSocket未连接时才尝试重连
    if (userStore.token && (connectionStatus.value === 'disconnected' || connectionStatus.value === 'error')) {
      try {
        console.log('自动尝试重连WebSocket...')
        await webSocketStore.reconnect(userStore.token)
      } catch (error) {
        console.log('自动重连失败:', error.message || '未知错误')
      }
    }
  }, 30000) // 30秒检查一次
}

const stopAutoReconnect = () => {
  if (reconnectInterval) {
    clearInterval(reconnectInterval)
    reconnectInterval = null
  }
}

// 组件挂载时启动自动重连机制
onMounted(() => {
  startAutoReconnect()
})

// 组件卸载时停止自动重连机制
onUnmounted(() => {
  stopAutoReconnect()
})
</script>

<style scoped>
.websocket-status {
  display: inline-block;
}

.status-badge {
  cursor: pointer;
}

.status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-indicator:hover {
  transform: scale(1.1);
}

.status-indicator.connected {
  background-color: #67c23a;
  color: white;
}

.status-indicator.connecting {
  background-color: #e6a23c;
  color: white;
  animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
  background-color: #f56c6c;
  color: white;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>