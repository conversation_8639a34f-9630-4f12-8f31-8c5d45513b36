# WebSocket 持久连接实现文档

## 概述

本文档描述了 WebSocket 持久连接功能的实现，确保用户在登录状态下始终保持 WebSocket 连接，并在断线时自动重连。

## 核心特性

### 1. 持久连接
- ✅ 登录后自动建立 WebSocket 连接
- ✅ 连接断开时自动重连（无限重连）
- ✅ 页面可见性变化时检查并恢复连接
- ✅ 网络状态变化时自动重连
- ✅ 指数退避重连策略（避免频繁重连）

### 2. 连接管理
- ✅ 登出时自动关闭连接并禁用重连
- ✅ 手动重连功能
- ✅ 连接状态实时监控
- ✅ 心跳机制保持连接活跃

### 3. 消息处理
- ✅ 消息类型分发处理
- ✅ 未读消息计数
- ✅ 消息历史记录
- ✅ 通知提醒功能

## 文件修改说明

### 1. WebSocket 管理器 (`web/src/utils/websocket.js`)

**主要改进：**
- 添加持久连接模式支持
- 实现页面可见性和网络状态监听
- 指数退避重连策略
- 无限重连机制（只要用户登录就持续尝试）

**新增方法：**
- `setupPersistentConnection()` - 设置持久连接监听器
- `removePersistentConnection()` - 移除持久连接监听器
- `enablePersistentConnection()` - 启用持久连接
- `disablePersistentConnection()` - 禁用持久连接
- `forceReconnect()` - 强制重连

### 2. WebSocket Store (`web/src/pinia/modules/websocket.js`)

**主要改进：**
- 支持持久连接模式的初始化
- 新增持久连接控制方法
- 改进错误处理逻辑

**新增方法：**
- `enablePersistentConnection()` - 启用持久连接
- `disablePersistentConnection()` - 禁用持久连接
- `forceReconnect()` - 强制重连

### 3. 用户 Store (`web/src/pinia/modules/user.js`)

**主要改进：**
- 登录时自动启用 WebSocket 持久连接
- 登出时正确关闭持久连接
- 移除旧的页面可见性处理逻辑（现在由 WebSocketManager 处理）

### 4. 应用初始化 (`web/src/utils/websocketInit.js`)

**新增文件，提供：**
- 应用启动时的 WebSocket 初始化
- 用户登录状态检查
- 连接状态查询工具
- 手动重连工具

### 5. 权限管理 (`web/src/permission.js`)

**主要改进：**
- 应用启动时自动初始化 WebSocket 连接
- 检查用户登录状态并建立持久连接

### 6. 状态组件 (`web/src/components/WebSocketStatus.vue`)

**主要改进：**
- 使用新的强制重连功能
- 移除旧的组件级自动重连逻辑
- 改进用户交互体验

## 使用方法

### 1. 自动初始化
系统会在应用启动时自动检查用户登录状态并建立 WebSocket 连接，无需手动操作。

### 2. 手动控制
```javascript
import { useWebSocketStore } from '@/pinia/modules/websocket'

const webSocketStore = useWebSocketStore()

// 启用持久连接
webSocketStore.enablePersistentConnection()

// 禁用持久连接
webSocketStore.disablePersistentConnection()

// 强制重连
await webSocketStore.forceReconnect()

// 检查连接状态
console.log(webSocketStore.isConnected)
```

### 3. 状态监控
```javascript
import { getWebSocketStatus } from '@/utils/websocketInit'

const status = getWebSocketStatus()
console.log('连接状态:', status.connectionStatus)
console.log('是否连接:', status.isConnected)
console.log('未读消息:', status.unreadCount)
```

## 重连策略

### 1. 指数退避
- 初始重连间隔：5秒
- 最大重连间隔：60秒
- 退避因子：1.5
- 连接成功后重置间隔

### 2. 触发条件
- WebSocket 连接错误
- WebSocket 连接关闭（非手动）
- 页面从隐藏变为可见
- 网络从离线变为在线

### 3. 停止条件
- 用户手动登出
- 手动关闭连接
- 禁用持久连接

## 配置说明

### WebSocket 连接配置
```javascript
// 在 web/src/utils/websocket.js 中
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' // 固定token
const url = 'ws://**************:82/api/msgSocket?token=${token}'
```

### 重连参数
```javascript
this.maxReconnectAttempts = Infinity // 无限重连
this.reconnectInterval = 5000 // 初始间隔5秒
this.maxReconnectInterval = 60000 // 最大间隔60秒
this.heartbeatTimer = 30000 // 心跳间隔30秒
```

## 监控和调试

### 1. 控制台日志
系统会输出详细的连接状态日志，包括：
- 连接建立/断开
- 重连尝试
- 消息收发
- 错误信息

### 2. 状态组件
页面右上角的 WebSocket 状态指示器显示：
- 🟢 绿色：已连接
- 🟡 黄色：连接中
- 🔴 红色：未连接

点击状态指示器可以：
- 手动重连（断开状态）
- 清除未读消息（连接状态）

### 3. 开发工具
在浏览器开发者工具中可以：
- 查看 WebSocket 连接状态
- 监控消息收发
- 检查网络请求

## 注意事项

1. **Token 管理**：当前使用固定 token，生产环境需要使用动态 token
2. **网络环境**：在网络不稳定的环境下，重连可能会比较频繁
3. **资源消耗**：持久连接会持续消耗一定的系统资源
4. **服务器支持**：需要服务器端支持 WebSocket 长连接和重连

## 故障排除

### 1. 连接失败
- 检查网络连接
- 确认服务器地址和端口
- 验证 token 有效性

### 2. 频繁重连
- 检查服务器稳定性
- 确认网络环境
- 查看控制台错误日志

### 3. 消息丢失
- 确认连接状态
- 检查消息处理器
- 查看服务器日志
