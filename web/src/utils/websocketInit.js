/**
 * WebSocket 初始化工具
 * 用于在应用启动时检查登录状态并自动建立WebSocket连接
 */

import { useUserStore } from '@/pinia/modules/user'
import { useWebSocketStore } from '@/pinia/modules/websocket'

/**
 * 初始化WebSocket连接
 * 检查用户登录状态，如果已登录则自动建立持久连接
 */
export const initWebSocketConnection = async () => {
  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()
    
    // 检查用户是否已登录
    if (userStore.token) {
      console.log('检测到用户已登录，初始化WebSocket持久连接')
      
      // 启用持久连接
      webSocketStore.enablePersistentConnection()
      
      // 尝试建立连接
      try {
        const connected = await webSocketStore.initConnection(userStore.token, true)
        if (connected) {
          console.log('WebSocket持久连接初始化成功')
        } else {
          console.log('WebSocket初始连接失败，但持久连接机制已启用')
        }
      } catch (error) {
        console.log('WebSocket初始连接失败，持久连接机制将在后台重试:', error.message)
      }
    } else {
      console.log('用户未登录，跳过WebSocket连接初始化')
    }
  } catch (error) {
    console.error('WebSocket连接初始化过程出错:', error)
  }
}

/**
 * 监听用户登录状态变化
 * 当用户登录时自动建立WebSocket连接，登出时关闭连接
 */
export const watchUserLoginStatus = () => {
  // 这个函数可以在需要时扩展，目前登录/登出逻辑已在user store中处理
  console.log('WebSocket用户状态监听器已设置')
}

/**
 * 获取WebSocket连接状态信息
 */
export const getWebSocketStatus = () => {
  try {
    const webSocketStore = useWebSocketStore()
    return {
      isConnected: webSocketStore.isConnected,
      isConnecting: webSocketStore.isConnecting,
      connectionStatus: webSocketStore.connectionStatus,
      autoReconnectEnabled: webSocketStore.autoReconnectEnabled,
      unreadCount: webSocketStore.unreadCount
    }
  } catch (error) {
    console.error('获取WebSocket状态失败:', error)
    return {
      isConnected: false,
      isConnecting: false,
      connectionStatus: 'disconnected',
      autoReconnectEnabled: false,
      unreadCount: 0
    }
  }
}

/**
 * 手动重连WebSocket
 */
export const reconnectWebSocket = async () => {
  try {
    const userStore = useUserStore()
    const webSocketStore = useWebSocketStore()

    if (!userStore.token) {
      throw new Error('用户未登录，无法重连WebSocket')
    }

    console.log('手动重连WebSocket')
    await webSocketStore.forceReconnect(userStore.token)
    console.log('WebSocket重连成功')
    return true
  } catch (error) {
    console.error('WebSocket重连失败:', error)
    throw error
  }
}

/**
 * 应用启动时的WebSocket初始化
 * 应该在应用的main.js或app初始化时调用
 */
export const setupWebSocketForApp = async () => {
  console.log('开始设置应用级WebSocket连接')
  
  // 初始化连接
  await initWebSocketConnection()
  
  // 设置状态监听
  watchUserLoginStatus()
  
  console.log('应用级WebSocket设置完成')
}
