/**
 * WebSocket 连接管理类
 * 用于管理与服务器的WebSocket连接
 */
class WebSocketManager {
  constructor() {
    this.ws = null
    this.url = ''
    this.token = ''
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.heartbeatInterval = null
    this.heartbeatTimer = 30000 // 30秒心跳
    this.isManualClose = false
    this.messageHandlers = new Map()
    this.connectionStatus = 'disconnected' // disconnected, connecting, connected
    this.reconnectTimer = null // 重连定时器
  }

  /**
   * 初始化WebSocket连接
   * @param {string} token - 用户token
   */
  connect(token) {
    if (!token) {
      console.error('WebSocket连接失败：token不能为空')
      return Promise.reject(new Error('token不能为空'))
    }

    // this.token = token
    this.token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJEYXRhIjp7ImlwaG9uZV9jb2RlIjoiIiwidXNlcl9pZCI6MTAwMDMsImlzX2xvZ2luIjp0cnVlfSwiZXhwIjoxNzU2NDM3ODQ3LCJuYmYiOjE3NTM4NDU4NDcsImlhdCI6MTc1Mzg0NTg0N30.OflBjUMWWU9_eG_349GkCtSqqKOevZpsm_f_mW6YClY'
    this.url = `ws://**************:82/api/msgSocket?token=${token}`
    this.isManualClose = false

    return new Promise((resolve, reject) => {
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          console.log('WebSocket已连接，无需重复连接')
          resolve()
          return
        }

        this.connectionStatus = 'connecting'
        console.log('正在连接WebSocket...', this.url)
        
        this.ws = new WebSocket(this.url)

        // 连接成功
        this.ws.onopen = () => {
          console.log('WebSocket连接成功')
          this.connectionStatus = 'connected'
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('WebSocket消息解析失败:', error, event.data)
          }
        }

        // 连接错误
        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.connectionStatus = 'disconnected'
          // 发生错误时尝试重连
          this.scheduleReconnect()
          reject(error)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.connectionStatus = 'disconnected'
          this.stopHeartbeat()
          
          // 如果不是手动关闭，则尝试重连
          if (!this.isManualClose) {
            this.scheduleReconnect()
          }
        }

      } catch (error) {
        console.error('WebSocket连接异常:', error)
        this.connectionStatus = 'disconnected'
        // 连接异常时尝试重连
        this.scheduleReconnect()
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息
   * @param {Object} data - 消息数据
   */
  handleMessage(data) {
    console.log('收到WebSocket消息:', data)
    
    // 心跳响应不需要特殊处理
    if (data.type === 'heartbeat' || data.type === 0) {
      return
    }

    // 触发消息处理器
    this.messageHandlers.forEach((handler, type) => {
      if (type === 'all' || data.type === type) {
        try {
          handler(data)
        } catch (error) {
          console.error('消息处理器执行错误:', error)
        }
      }
    })
  }

  /**
   * 发送消息
   * @param {Object} data - 要发送的数据
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data)
        this.ws.send(message)
        console.log('发送WebSocket消息:', data)
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    } else {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
  }

  /**
   * 添加消息处理器
   * @param {string|number} type - 消息类型，'all'表示处理所有消息
   * @param {Function} handler - 处理函数
   */
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   * @param {string|number} type - 消息类型
   */
  removeMessageHandler(type) {
    this.messageHandlers.delete(type)
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 0 }) // 发送心跳包
      }
    }, this.heartbeatTimer)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    // 清除之前的重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    this.reconnectAttempts++
    console.log(`WebSocket重连中... (${this.reconnectAttempts}次尝试)`)
    
    this.reconnectTimer = setTimeout(() => {
      // 只要不是手动关闭且有token就尝试重连
      if (!this.isManualClose && this.token) {
        this.connect(this.token).catch(error => {
          console.error('WebSocket重连失败:', error)
          // 如果达到最大重连次数，仍然继续尝试，但增加间隔时间
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，但将继续尝试保持连接')
          }
          // 继续安排重连
          this.scheduleReconnect()
        })
      }
    }, this.reconnectInterval)
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true
    
    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.connectionStatus = 'disconnected'
    this.messageHandlers.clear()
    console.log('WebSocket连接已手动关闭')
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return this.connectionStatus
  }

  /**
   * 检查是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }
}

// 创建全局WebSocket管理器实例
const webSocketManager = new WebSocketManager()

export default webSocketManager