import { login, getUserInfo } from '@/api/user'
import { jsonInBlacklist } from '@/api/jwt'
import router from '@/router/index'
import { ElLoading, ElMessage } from 'element-plus'
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useRouterStore } from './router'
import { useCookies } from '@vueuse/integrations/useCookies'
import { useStorage } from '@vueuse/core'

import { useAppStore } from '@/pinia'

export const useUserStore = defineStore('user', () => {
  const appStore = useAppStore()
  const loadingInstance = ref(null)

  const userInfo = ref({
    uuid: '',
    nickName: '',
    headerImg: '',
    authority: {}
  })
  const token = useStorage('token', '')
  const xToken = useCookies('x-token')
  const currentToken = computed(() => token.value || xToken.value || '')

  const setUserInfo = (val) => {
    userInfo.value = val
    if (val.originSetting) {
      Object.keys(appStore.config).forEach((key) => {
        if (val.originSetting[key] !== undefined) {
          appStore.config[key] = val.originSetting[key]
        }
      })
    }
    console.log(appStore.config)
  }

  const setToken = (val) => {
    token.value = val
    xToken.value = val
  }

  const NeedInit = async () => {
    await ClearStorage()
    await router.push({ name: 'Init', replace: true })
  }

  const ResetUserInfo = (value = {}) => {
    userInfo.value = {
      ...userInfo.value,
      ...value
    }
  }
  /* 获取用户信息*/
  const GetUserInfo = async () => {
    const res = await getUserInfo()
    if (res.code === 0) {
      setUserInfo(res.data.userInfo)
    }
    return res
  }
  /* 登录*/
  const LoginIn = async (loginInfo) => {
    try {
      loadingInstance.value = ElLoading.service({
        fullscreen: true,
        text: '登录中，请稍候...'
      })

      const res = await login(loginInfo)

      if (res.code !== 0) {
        ElMessage.error(res.message || '登录失败')
        return false
      }
      // 登陆成功，设置用户信息和权限相关信息
      setUserInfo(res.data.user)
      setToken(res.data.token)

      // 初始化路由信息
      const routerStore = useRouterStore()
      await routerStore.SetAsyncRouter()
      const asyncRouters = routerStore.asyncRouters

      // 注册到路由表里
      asyncRouters.forEach((asyncRouter) => {
        router.addRoute(asyncRouter)
      })

      if (!router.hasRoute(userInfo.value.authority.defaultRouter)) {
        ElMessage.error('请联系管理员进行授权')
      } else {
        await router.replace({ name: userInfo.value.authority.defaultRouter })
      }

      const isWindows = /windows/i.test(navigator.userAgent)
      window.localStorage.setItem('osType', isWindows ? 'WIN' : 'MAC')

      // 初始化WebSocket连接
      try {
        // 动态导入WebSocket store以避免循环依赖
        const { useWebSocketStore } = await import('@/pinia/modules/websocket')
        const webSocketStore = useWebSocketStore()
        await webSocketStore.initConnection(res.data.token)
        console.log('WebSocket连接初始化成功')
        
        // 监听页面可见性变化，处理连接状态
        handleVisibilityChange(res.data.token)
      } catch (error) {
        console.error('WebSocket连接初始化失败:', error)
        // WebSocket连接失败不影响登录流程，只记录错误
      }

      // 全部操作均结束，关闭loading并返回
      return true
    } catch (error) {
      console.error('LoginIn error:', error)
      return false
    } finally {
      loadingInstance.value?.close()
    }
  }
  
  // 处理页面可见性变化，确保WebSocket连接
  const handleVisibilityChange = (token) => {
    const handleVisibilityChangeFn = async () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时检查WebSocket连接
        try {
          const { useWebSocketStore } = await import('@/pinia/modules/websocket')
          const webSocketStore = useWebSocketStore()
          
          // 如果没有连接，则尝试重新连接
          if (!webSocketStore.isConnected) {
            console.log('页面可见，检查WebSocket连接状态')
            try {
              await webSocketStore.reconnect(token)
              console.log('WebSocket连接已恢复')
            } catch (error) {
              console.error('恢复WebSocket连接失败:', error)
            }
          }
        } catch (error) {
          console.error('处理页面可见性变化时出错:', error)
        }
      }
    }
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChangeFn)
  }

  /* 登出*/
  const LoginOut = async () => {
    const res = await jsonInBlacklist()

    // 登出失败
    if (res.code !== 0) {
      return
    }

    // 关闭WebSocket连接
    try {
      // 动态导入WebSocket store以避免循环依赖
      const { useWebSocketStore } = await import('@/pinia/modules/websocket')
      const webSocketStore = useWebSocketStore()
      webSocketStore.closeConnection()
      console.log('WebSocket连接已关闭')
    } catch (error) {
      console.error('关闭WebSocket连接时出错:', error)
    }

    await ClearStorage()

    // 把路由定向到登录页，无需等待直接reload
    router.push({ name: 'Login', replace: true })
    window.location.reload()
  }
  /* 清理数据 */
  const ClearStorage = async () => {
    token.value = ''
    xToken.value = ''
    sessionStorage.clear()
    localStorage.removeItem('originSetting')
  }

  return {
    userInfo,
    token: currentToken,
    NeedInit,
    ResetUserInfo,
    GetUserInfo,
    LoginIn,
    LoginOut,
    setToken,
    loadingInstance,
    ClearStorage
  }
})